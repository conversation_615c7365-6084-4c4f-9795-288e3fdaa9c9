import time
import threading
import traceback
import json
from typing import Dict, Any, Optional, List, Set
from dataclasses import dataclass, asdict
from java.util import ArrayList
from java.lang import Integer as jint

from base_plugin import BasePlugin, <PERSON><PERSON><PERSON><PERSON>, HookStrategy
from client_utils import get_messages_controller, get_user_config, run_on_ui_thread, run_on_queue, PLUGINS_QUEUE
from ui.settings import Header, Input, Switch, Divider, Text, Selector
from ui.bulletin import BulletinHelper
from android_utils import log
from org.telegram.tgnet import TLRPC

__id__ = "auto_message_deleter"
__name__ = "Auto Message Deleter"
__description__ = "Автоматически удаляет отправленные сообщения через настраиваемое время с поддержкой синхронизации между устройствами"
__author__ = "@mihai<PERSON><PERSON><PERSON><PERSON> & @mishabotov"
__version__ = "1.2.0"
__min_version__ = "11.12.0"
__icon__ = "DateRegBot_by_MoiStikiBot/12"

@dataclass
class PendingMessage:
    """Информация о сообщении, ожидающем удаления"""
    message_id: int
    chat_id: int
    topic_id: int
    timestamp: float
    delete_time: float = 0.0
    delete_for_all: bool = True

    def to_dict(self) -> Dict[str, Any]:
        """Конвертация в словарь для сериализации"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PendingMessage':
        """Создание из словаря"""
        return cls(**data)

class AutoMessageDeleterPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.pending_messages: Dict[str, PendingMessage] = {}
        self.pending_sends: Dict[int, Dict[str, Any]] = {}  # random_id -> send_params
        self.lock = threading.Lock()
        self.last_sent_messages = []  # Список последних отправленных сообщений для fallback
        self.background_thread: Optional[threading.Thread] = None
        self.stop_background_thread = False
        self.STORAGE_KEY = "auto_message_deleter_persistent_tasks"
        self.bulletin_shown_messages: Set[str] = set()  # Для предотвращения дублирования уведомлений

    def on_plugin_load(self):
        """Инициализация плагина"""
        self.add_on_send_message_hook()
        # Добавляем хуки для перехвата ответов на отправку сообщений
        self.add_hook("TL_messages_sendMessage")
        self.add_hook("TL_messages_sendMedia")
        self.add_hook("TL_messages_sendInlineBotResult")
        self.add_hook("TL_messages_forwardMessages")
        self.add_hook("TL_messages_sendMultiMedia")
        # Добавляем хуки для перехвата обновлений о новых сообщениях
        # ИСПРАВЛЕНИЕ: Добавляем поддержку сообщений в каналах/группах
        self.add_hook("TL_updateNewMessage")
        self.add_hook("TL_updateNewChannelMessage")  # Для каналов и групп
        self.add_hook("TL_updateMessageID")  # Для отслеживания ID сообщений

        # Загружаем сохраненные задачи и запускаем фоновый поток
        self._load_pending_tasks()
        self._start_background_processor()

        # Логируем настройки синхронизации
        sync_enabled = self.get_setting("sync_devices", True)
        self.log(f"Auto Message Deleter (Enhanced Sync) plugin loaded - Device sync: {sync_enabled}")

    def on_plugin_unload(self):
        """Очистка ресурсов при выгрузке плагина"""
        # Останавливаем фоновый поток
        self.stop_background_thread = True
        if self.background_thread and self.background_thread.is_alive():
            self.background_thread.join(timeout=2.0)

        # Сохраняем текущие задачи
        self._save_pending_tasks()

        with self.lock:
            self.pending_messages.clear()
            self.pending_sends.clear()
            self.bulletin_shown_messages.clear()
        self.log("Auto Message Deleter (Enhanced) plugin unloaded")
        
    def create_settings(self):
        """Создание интерфейса настроек"""
        return [
            Header(text="Настройки автоудаления"),
            Switch(
                key="enabled",
                text="Включить автоудаление",
                default=False,
                subtext="Автоматически удалять отправленные сообщения",
                icon="msg_delete"
            ),
            Selector(
                key="delete_time",
                text="Время до удаления",
                default=2,  # 30 секунд
                items=["5 секунд", "10 секунд", "30 секунд", "1 минута", "2 минуты", "5 минут", "10 минут"],
                icon="msg_timer"
            ),
            Switch(
                key="delete_for_all",
                text="Удалять для всех",
                default=True,
                subtext="Удалять сообщения для всех участников чата",
                icon="msg_delete"
            ),
            Switch(
                key="show_notifications",
                text="Показывать уведомления",
                default=True,
                subtext="Показывать уведомления об удалении сообщений",
                icon="msg_info"
            ),
            Switch(
                key="sync_devices",
                text="Синхронизация между устройствами",
                default=True,
                subtext="Удалять сообщения, отправленные с других устройств (ПК, веб)",
                icon="msg_sync"
            ),
            Divider(),
            Header(text="Статистика"),
            Text(
                text=f"Активных таймеров: {len(self.pending_messages)}",
                icon="msg_stats"
            ),
            Text(
                text="Показать отладочную информацию",
                icon="msg_info",
                on_click=self._show_debug_info
            ),
            Text(
                text="Очистить все таймеры",
                icon="msg_clear",
                red=True,
                on_click=self._clear_all_timers
            ),
            Divider(text="Плагин автоматически удаляет ваши отправленные сообщения через указанное время")
        ]

    def _clear_all_timers(self, view):
        """Очистка всех активных таймеров"""
        with self.lock:
            count = len(self.pending_messages)
            self.pending_messages.clear()

        # Очищаем сохраненные задачи
        self._save_pending_tasks()

        if count > 0:
            run_on_ui_thread(lambda: BulletinHelper.show_success(f"Отменено {count} задач удаления"))
        else:
            run_on_ui_thread(lambda: BulletinHelper.show_info("Нет активных задач"))

    def _show_debug_info(self, view):
        """Показать отладочную информацию"""
        try:
            with self.lock:
                pending_count = len(self.pending_messages)
                bulletin_count = len(self.bulletin_shown_messages)
                sends_count = len(self.pending_sends)

                debug_info = []
                debug_info.append(f"Активных задач: {pending_count}")
                debug_info.append(f"Показанных уведомлений: {bulletin_count}")
                debug_info.append(f"Ожидающих отправок: {sends_count}")

                if self.pending_messages:
                    debug_info.append("\nАктивные задачи:")
                    for key, msg in list(self.pending_messages.items())[:5]:  # Показываем первые 5
                        chat_type = "приватный" if msg.chat_id > 0 else "группа/канал"
                        time_left = max(0, msg.delete_time - time.time())
                        debug_info.append(f"• ID {msg.message_id} ({chat_type}): {time_left:.1f}с")

                debug_text = "\n".join(debug_info)

            run_on_ui_thread(lambda: BulletinHelper.show_info(debug_text))

        except Exception as e:
            self.log(f"Error showing debug info: {e}")
            run_on_ui_thread(lambda: BulletinHelper.show_error("Ошибка получения отладочной информации"))

    def _get_delete_delay(self) -> float:
        """Получение времени задержки до удаления в секундах"""
        time_index = self.get_setting("delete_time", 2)
        time_values = [5, 10, 30, 60, 120, 300, 600]  # в секундах
        return float(time_values[min(time_index, len(time_values) - 1)])
        
    def on_send_message_hook(self, account: int, params: Any) -> HookResult:
        """Перехват отправки сообщений"""
        if not self.get_setting("enabled", False):
            return HookResult()

        try:
            self.log(f"on_send_message_hook called with params: {type(params)}")

            # Генерируем уникальный ID для сопоставления с ответом
            send_timestamp = time.time()

            # ИСПРАВЛЕНИЕ: Пытаемся получить random_id для лучшего отслеживания
            random_id = None
            try:
                if hasattr(params, 'random_id'):
                    random_id = params.random_id
                elif hasattr(params, 'randomId'):
                    random_id = params.randomId
            except Exception as e:
                self.log(f"Could not get random_id: {e}")

            # Сохраняем параметры отправки
            with self.lock:
                # Используем timestamp как ключ, так как random_id может быть недоступен
                key = f"{send_timestamp}_{id(params)}"
                send_data = {
                    'peer': getattr(params, 'peer', None),
                    'timestamp': send_timestamp,
                    'topic_id': self._get_topic_id(params),
                    'params_id': id(params)
                }

                # Добавляем random_id если доступен
                if random_id is not None:
                    send_data['random_id'] = random_id
                    self.log(f"Saved send params with random_id: {random_id}")

                self.pending_sends[key] = send_data

            self.log(f"Saved send params with key: {key}")

        except Exception as e:
            self.log(f"Error in on_send_message_hook: {e}")
            self.log(f"Traceback: {traceback.format_exc()}")

        return HookResult()
        
    def post_request_hook(self, request_name: str, account: int, response: Any, error: Any) -> HookResult:
        """Перехват ответов на отправку сообщений"""
        if not self.get_setting("enabled", False):
            return HookResult()

        if error:
            self.log(f"Request {request_name} failed with error: {error}")
            return HookResult()

        try:
            self.log(f"post_request_hook called: {request_name}, response type: {type(response)}")

            if request_name in ["TL_messages_sendMessage", "TL_messages_sendMedia",
                              "TL_messages_sendInlineBotResult", "TL_messages_forwardMessages",
                              "TL_messages_sendMultiMedia"]:
                self._process_send_response(response, request_name)

        except Exception as e:
            self.log(f"Error in post_request_hook: {e}")
            self.log(f"Traceback: {traceback.format_exc()}")

        return HookResult()

    def on_update_hook(self, update_name: str, account: int, update: Any) -> HookResult:
        """Перехват обновлений о новых сообщениях"""
        if not self.get_setting("enabled", False):
            return HookResult()

        try:
            # ИСПРАВЛЕНИЕ: Обрабатываем как обычные сообщения, так и сообщения в каналах
            if update_name in ["TL_updateNewMessage", "TL_updateNewChannelMessage"]:
                self.log(f"Received {update_name} update")

                try:
                    message = update.message
                    if message:
                        # Проверяем, что это наше сообщение
                        current_user_id = get_user_config().getClientUserId()

                        # Безопасная проверка from_id
                        is_our_message = False
                        try:
                            if message.from_id and hasattr(message.from_id, 'user_id'):
                                is_our_message = message.from_id.user_id == current_user_id
                            elif hasattr(message, 'user_id'):
                                is_our_message = message.user_id == current_user_id
                        except Exception as e:
                            self.log(f"Error checking message sender: {e}")

                        # ИСПРАВЛЕНИЕ: Проверяем настройку синхронизации устройств
                        sync_devices = self.get_setting("sync_devices", True)

                        if is_our_message:
                            # Дополнительная проверка: если синхронизация отключена,
                            # обрабатываем только сообщения, отправленные локально
                            should_process = True
                            if not sync_devices:
                                # Проверяем, было ли сообщение отправлено с этого устройства
                                # Если сообщение появилось слишком быстро после отправки,
                                # вероятно оно было отправлено локально
                                current_time = time.time()
                                with self.lock:
                                    recent_sends = [s for s in self.pending_sends.values()
                                                  if current_time - s['timestamp'] < 5.0]
                                    if not recent_sends:
                                        should_process = False
                                        self.log(f"Skipping message {message.id} - sync disabled and no recent local sends")

                            if should_process:
                                try:
                                    msg_id = message.id
                                    self.log(f"Found our message with ID: {msg_id} (sync_devices: {sync_devices})")

                                    # Добавляем в список последних отправленных сообщений
                                    with self.lock:
                                        self.last_sent_messages.append({
                                            'id': msg_id,
                                            'timestamp': time.time(),
                                            'chat_id': self._get_chat_id_from_message(message)
                                        })

                                        # Оставляем только последние 10 сообщений
                                        if len(self.last_sent_messages) > 10:
                                            self.last_sent_messages = self.last_sent_messages[-10:]

                                    # Планируем удаление
                                    self._schedule_message_deletion_from_update(message)
                                except Exception as e:
                                    self.log(f"Error processing our message: {e}")
                    else:
                        self.log(f"{update_name} has null message")
                except Exception as e:
                    self.log(f"Error accessing update.message: {e}")

            # ИСПРАВЛЕНИЕ: Обрабатываем TL_updateMessageID для отслеживания ID сообщений
            elif update_name == "TL_updateMessageID":
                try:
                    if hasattr(update, 'id') and hasattr(update, 'random_id'):
                        msg_id = update.id
                        random_id = update.random_id
                        self.log(f"Message ID update: {msg_id} for random_id: {random_id}")

                        # Пытаемся найти соответствующую отправку по random_id
                        with self.lock:
                            for key, send_data in list(self.pending_sends.items()):
                                # Если нашли соответствие, планируем удаление
                                if 'random_id' in send_data and send_data['random_id'] == random_id:
                                    self.log(f"Found matching send for message {msg_id}")
                                    self._schedule_message_deletion_by_id(msg_id, send_data)
                                    del self.pending_sends[key]
                                    break
                except Exception as e:
                    self.log(f"Error processing TL_updateMessageID: {e}")

        except Exception as e:
            self.log(f"Error in on_update_hook: {e}")
            self.log(f"Traceback: {traceback.format_exc()}")

        return HookResult()

    def on_updates_hook(self, container_name: str, account: int, updates: Any) -> HookResult:
        """Перехват батчей обновлений - важно для синхронизации между устройствами"""
        if not self.get_setting("enabled", False):
            return HookResult()

        if not self.get_setting("sync_devices", True):
            return HookResult()

        try:
            self.log(f"Received updates container: {container_name}")

            if hasattr(updates, 'updates') and updates.updates:
                current_user_id = get_user_config().getClientUserId()

                for i in range(updates.updates.size()):
                    try:
                        update = updates.updates.get(i)
                        update_class = update.__class__.__name__

                        # Обрабатываем сообщения в батчах обновлений
                        if "updateNewMessage" in update_class or "updateNewChannelMessage" in update_class:
                            if hasattr(update, 'message') and update.message:
                                message = update.message

                                # Проверяем, что это наше сообщение
                                is_our_message = False
                                try:
                                    if message.from_id and hasattr(message.from_id, 'user_id'):
                                        is_our_message = message.from_id.user_id == current_user_id
                                    elif hasattr(message, 'user_id'):
                                        is_our_message = message.user_id == current_user_id
                                except Exception as e:
                                    self.log(f"Error checking batch message sender: {e}")

                                if is_our_message:
                                    self.log(f"Found our message in batch: {message.id} (type: {update_class})")
                                    self._schedule_message_deletion_from_update(message)

                        # Обрабатываем обновления ID сообщений в батчах
                        elif "updateMessageID" in update_class:
                            if hasattr(update, 'id') and hasattr(update, 'random_id'):
                                msg_id = update.id
                                random_id = update.random_id
                                self.log(f"Batch message ID update: {msg_id} for random_id: {random_id}")

                                with self.lock:
                                    for key, send_data in list(self.pending_sends.items()):
                                        if 'random_id' in send_data and send_data['random_id'] == random_id:
                                            self.log(f"Found matching batch send for message {msg_id}")
                                            self._schedule_message_deletion_by_id(msg_id, send_data)
                                            del self.pending_sends[key]
                                            break

                    except Exception as e:
                        self.log(f"Error processing update {i} in batch: {e}")

        except Exception as e:
            self.log(f"Error in on_updates_hook: {e}")
            self.log(f"Traceback: {traceback.format_exc()}")

        return HookResult()

    def _get_chat_id_from_message(self, message: Any) -> int:
        """Получение ID чата из объекта сообщения"""
        try:
            # Безопасное получение peer_id
            peer = None
            try:
                peer = message.peer_id
            except Exception as e:
                self.log(f"Error accessing message.peer_id: {e}")
                return 0

            if peer:
                # Проверяем channel_id
                try:
                    if hasattr(peer, 'channel_id') and peer.channel_id != 0:
                        return -peer.channel_id
                except Exception as e:
                    self.log(f"Error accessing peer.channel_id: {e}")

                # Проверяем chat_id
                try:
                    if hasattr(peer, 'chat_id') and peer.chat_id != 0:
                        return -peer.chat_id
                except Exception as e:
                    self.log(f"Error accessing peer.chat_id: {e}")

                # Проверяем user_id
                try:
                    if hasattr(peer, 'user_id') and peer.user_id != 0:
                        return peer.user_id
                except Exception as e:
                    self.log(f"Error accessing peer.user_id: {e}")

        except Exception as e:
            self.log(f"Error getting chat ID from message: {e}")
        return 0

    def _schedule_message_deletion_from_update(self, message: Any):
        """Планирование удаления сообщения из обновления"""
        try:
            # Безопасное извлечение ID сообщения
            try:
                message_id = message.id
            except Exception as e:
                self.log(f"Error getting message.id: {e}")
                return

            chat_id = self._get_chat_id_from_message(message)
            topic_id = 0

            # Безопасное получение topic_id из сообщения
            try:
                if message.reply_to and hasattr(message.reply_to, 'reply_to_top_id'):
                    topic_id = message.reply_to.reply_to_top_id
            except Exception as e:
                self.log(f"Error getting topic_id: {e}")

            self.log(f"Scheduling deletion from update: msg_id={message_id}, chat_id={chat_id}, topic_id={topic_id}")

            # Проверяем, не запланировано ли уже удаление этого сообщения
            key = f"{chat_id}_{message_id}"
            with self.lock:
                if key in self.pending_messages:
                    self.log(f"Message {message_id} already scheduled for deletion, skipping")
                    return

            # Вычисляем время удаления
            delay = self._get_delete_delay()
            delete_time = time.time() + delay

            # Создаем объект ожидающего сообщения
            pending_msg = PendingMessage(
                message_id=message_id,
                chat_id=chat_id,
                topic_id=topic_id,
                timestamp=time.time(),
                delete_time=delete_time,
                delete_for_all=self.get_setting("delete_for_all", True)
            )

            # Сохраняем в список ожидающих
            with self.lock:
                self.pending_messages[key] = pending_msg

            # Сохраняем задачи в постоянное хранилище
            self._save_pending_tasks()

            self.log(f"Scheduled deletion for message {message_id}, delete at: {delete_time}")

            # Показываем уведомление только один раз для каждого сообщения
            if self.get_setting("show_notifications", True):
                with self.lock:
                    if key not in self.bulletin_shown_messages:
                        self.bulletin_shown_messages.add(key)
                        run_on_ui_thread(lambda: BulletinHelper.show_info(
                            f"Сообщение будет удалено через {int(delay)} сек."
                        ))

        except Exception as e:
            self.log(f"Error scheduling message deletion from update: {e}")
            self.log(f"Traceback: {traceback.format_exc()}")

    def _schedule_message_deletion_by_id(self, message_id: int, send_data: Dict[str, Any]):
        """Планирование удаления сообщения по ID с данными отправки"""
        try:
            self.log(f"Scheduling deletion by ID: {message_id}")

            # Получаем данные из send_data
            chat_id = 0
            topic_id = 0

            if 'peer' in send_data and send_data['peer']:
                chat_id = self._get_chat_id_from_peer(send_data['peer'])

            if 'topic_id' in send_data:
                topic_id = send_data['topic_id']

            self.log(f"Scheduling deletion by ID: msg_id={message_id}, chat_id={chat_id}, topic_id={topic_id}")

            # Проверяем, не запланировано ли уже удаление этого сообщения
            key = f"{chat_id}_{message_id}"
            with self.lock:
                if key in self.pending_messages:
                    self.log(f"Message {message_id} already scheduled for deletion, skipping")
                    return

            # Вычисляем время удаления
            delay = self._get_delete_delay()
            delete_time = time.time() + delay

            # Создаем объект ожидающего сообщения
            pending_msg = PendingMessage(
                message_id=message_id,
                chat_id=chat_id,
                topic_id=topic_id,
                timestamp=time.time(),
                delete_time=delete_time,
                delete_for_all=self.get_setting("delete_for_all", True)
            )

            # Сохраняем в список ожидающих
            with self.lock:
                self.pending_messages[key] = pending_msg

            # Сохраняем задачи в постоянное хранилище
            self._save_pending_tasks()

            self.log(f"Scheduled deletion by ID for message {message_id}, delete at: {delete_time}")

            # Показываем уведомление только один раз для каждого сообщения
            if self.get_setting("show_notifications", True):
                with self.lock:
                    if key not in self.bulletin_shown_messages:
                        self.bulletin_shown_messages.add(key)
                        run_on_ui_thread(lambda: BulletinHelper.show_info(
                            f"Сообщение будет удалено через {int(delay)} сек."
                        ))

        except Exception as e:
            self.log(f"Error scheduling message deletion by ID: {e}")
            self.log(f"Traceback: {traceback.format_exc()}")

    def _process_send_response(self, response: Any, request_name: str):
        """Обработка ответа на отправку сообщения"""
        if not response:
            self.log("No response received")
            return

        try:
            self.log(f"Processing response for {request_name}: {type(response)}")
            response_class = response.__class__.__name__
            self.log(f"Response class name: {response_class}")

            # Детальное логирование структуры ответа
            try:
                attrs = [attr for attr in dir(response) if not attr.startswith('_')]
                self.log(f"Response attributes: {attrs[:10]}")  # Первые 10 атрибутов
            except Exception as e:
                self.log(f"Error getting response attributes: {e}")

            message_ids = []

            # Обрабатываем TL_updateShortSentMessage
            if "updateShortSentMessage" in response_class:
                self.log(f"Processing updateShortSentMessage: {response_class}")
                try:
                    msg_id = response.id
                    message_ids.append(msg_id)
                    self.log(f"Found message ID from {response_class}: {msg_id}")
                except Exception as e:
                    self.log(f"Error accessing {response_class}.id: {e}")

            # Извлекаем ID сообщения из ответа с updates
            elif "TL_updates" in response_class:  # Поддерживаем TLRPC$TL_updates и TL_updatesCombined
                self.log(f"Processing updates response: {response_class}")
                try:
                    updates_array = response.updates
                    if updates_array:
                        self.log(f"Found updates array with {updates_array.size()} items")
                        # TLRPC.Updates с массивом обновлений
                        for i in range(updates_array.size()):
                            update = updates_array.get(i)
                            class_name = update.__class__.__name__
                            self.log(f"Processing update {i}: {class_name}")

                            # Логируем атрибуты обновления для диагностики
                            try:
                                update_attrs = [attr for attr in dir(update) if not attr.startswith('_')]
                                self.log(f"Update {i} attributes: {update_attrs[:10]}")
                            except Exception as e:
                                self.log(f"Error getting update attributes: {e}")

                            # Проверяем различные варианты названий классов
                            if "updateNewMessage" in class_name:
                                self.log(f"Found updateNewMessage: {class_name}")
                                try:
                                    message = update.message
                                    if message:
                                        msg_id = message.id
                                        message_ids.append(msg_id)
                                        self.log(f"Found message ID from {class_name}: {msg_id}")
                                    else:
                                        self.log(f"{class_name} has null message")
                                except Exception as e:
                                    self.log(f"Error accessing {class_name}.message: {e}")

                            elif "updateMessageID" in class_name:
                                self.log(f"Found updateMessageID: {class_name}")
                                try:
                                    msg_id = update.id
                                    message_ids.append(msg_id)
                                    self.log(f"Found message ID from {class_name}: {msg_id}")
                                except Exception as e:
                                    self.log(f"Error accessing {class_name}.id: {e}")
                            else:
                                self.log(f"Skipping update type: {class_name}")
                    else:
                        self.log("Updates array is null")
                except Exception as e:
                    self.log(f"Error accessing response.updates: {e}")

            # Проверяем одиночное обновление
            try:
                if hasattr(response, 'update') and response.update:
                    update = response.update
                    class_name = update.__class__.__name__
                    self.log(f"Processing single update: {class_name}")

                    if "updateNewMessage" in class_name:
                        try:
                            message = update.message
                            if message:
                                msg_id = message.id
                                message_ids.append(msg_id)
                                self.log(f"Found message ID from single {class_name}: {msg_id}")
                        except Exception as e:
                            self.log(f"Error accessing single {class_name}.message: {e}")

                    elif "updateMessageID" in class_name:
                        try:
                            msg_id = update.id
                            message_ids.append(msg_id)
                            self.log(f"Found message ID from single {class_name}: {msg_id}")
                        except Exception as e:
                            self.log(f"Error accessing single {class_name}.id: {e}")
            except Exception as e:
                self.log(f"Error checking single update: {e}")

            # Планируем удаление для всех найденных сообщений
            self.log(f"Total message IDs found: {len(message_ids)}")
            for message_id in message_ids:
                self.log(f"Scheduling deletion for message ID: {message_id}")
                self._schedule_message_deletion_simple(message_id)

        except Exception as e:
            self.log(f"Error processing send response: {e}")
            self.log(f"Traceback: {traceback.format_exc()}")
            
    def _schedule_message_deletion_simple(self, message_id: int):
        """Упрощенное планирование удаления сообщения"""
        try:
            self.log(f"Scheduling deletion for message ID: {message_id}")

            # Получаем текущий фрагмент для определения чата
            chat_id = 0
            topic_id = 0

            try:
                from client_utils import get_last_fragment
                fragment = get_last_fragment()
                if fragment:
                    self.log(f"Fragment type: {type(fragment)}")

                    # Пробуем разные способы получения dialog_id
                    if hasattr(fragment, 'getDialogId'):
                        chat_id = fragment.getDialogId()
                        self.log(f"Got chat_id from getDialogId(): {chat_id}")
                    elif hasattr(fragment, 'dialog_id'):
                        chat_id = fragment.dialog_id
                        self.log(f"Got chat_id from dialog_id: {chat_id}")
                    elif hasattr(fragment, 'currentAccount') and hasattr(fragment, 'dialog_id'):
                        chat_id = fragment.dialog_id
                        self.log(f"Got chat_id from dialog_id (alt): {chat_id}")

                    # Пробуем получить topic_id
                    if hasattr(fragment, 'threadMessageId'):
                        topic_id = getattr(fragment, 'threadMessageId', 0)
                        self.log(f"Got topic_id: {topic_id}")
                else:
                    self.log("No fragment available")
            except Exception as fragment_error:
                self.log(f"Error getting fragment: {fragment_error}")

            # Если не удалось получить chat_id из фрагмента, пробуем альтернативные способы
            if chat_id == 0:
                self.log("Trying alternative methods to get chat_id")

                # Пробуем получить из последних отправленных сообщений
                with self.lock:
                    if self.last_sent_messages:
                        latest_msg = self.last_sent_messages[-1]
                        if time.time() - latest_msg['timestamp'] < 5:  # Если сообщение отправлено недавно
                            chat_id = latest_msg['chat_id']
                            self.log(f"Got chat_id from last sent messages: {chat_id}")

            self.log(f"Final Chat ID: {chat_id}, Topic ID: {topic_id}")

            if chat_id == 0:
                self.log("Could not determine chat ID, cannot schedule deletion")
                return

            # Проверяем, не запланировано ли уже удаление этого сообщения
            key = f"{chat_id}_{message_id}"
            with self.lock:
                if key in self.pending_messages:
                    self.log(f"Message {message_id} already scheduled for deletion, skipping")
                    return

            # Вычисляем время удаления
            delay = self._get_delete_delay()
            delete_time = time.time() + delay

            # Создаем объект ожидающего сообщения
            pending_msg = PendingMessage(
                message_id=message_id,
                chat_id=chat_id,
                topic_id=topic_id,
                timestamp=time.time(),
                delete_time=delete_time,
                delete_for_all=self.get_setting("delete_for_all", True)
            )

            # Сохраняем в список ожидающих
            with self.lock:
                self.pending_messages[key] = pending_msg

            # Сохраняем задачи в постоянное хранилище
            self._save_pending_tasks()

            self.log(f"Scheduled deletion for message {message_id}, delete at: {delete_time}")

            # Показываем уведомление только один раз для каждого сообщения
            if self.get_setting("show_notifications", True):
                with self.lock:
                    if key not in self.bulletin_shown_messages:
                        self.bulletin_shown_messages.add(key)
                        run_on_ui_thread(lambda: BulletinHelper.show_info(
                            f"Сообщение будет удалено через {int(delay)} сек."
                        ))

        except Exception as e:
            self.log(f"Error scheduling message deletion: {e}")
            self.log(f"Traceback: {traceback.format_exc()}")
            
    def _delete_message(self, pending_msg: PendingMessage):
        """Удаление сообщения с улучшенной обработкой для разных типов чатов"""
        try:
            self.log(f"Attempting to delete message {pending_msg.message_id} in chat {pending_msg.chat_id}")

            # Удаляем из списка ожидающих и уведомлений
            key = f"{pending_msg.chat_id}_{pending_msg.message_id}"
            with self.lock:
                self.pending_messages.pop(key, None)
                self.bulletin_shown_messages.discard(key)

            # Обновляем сохраненные задачи
            self._save_pending_tasks()

            # Выполняем удаление в главном UI потоке
            def delete_in_main_thread():
                try:
                    # Создаем список ID сообщений для удаления
                    msgs_list = ArrayList()
                    msgs_list.add(jint(pending_msg.message_id))

                    # Определяем тип чата для правильного удаления
                    is_private_chat = pending_msg.chat_id > 0
                    is_channel_or_supergroup = pending_msg.chat_id < 0

                    self.log(f"Deleting message: ID={pending_msg.message_id}, "
                            f"chat_id={pending_msg.chat_id}, topic_id={pending_msg.topic_id}, "
                            f"delete_for_all={pending_msg.delete_for_all}, "
                            f"is_private={is_private_chat}, is_channel={is_channel_or_supergroup}")

                    # Для приватных чатов может потребоваться особая обработка delete_for_all
                    delete_for_all = pending_msg.delete_for_all
                    if is_private_chat:
                        # В приватных чатах delete_for_all работает только в течение определенного времени
                        # Проверяем, не слишком ли старое сообщение
                        message_age = time.time() - pending_msg.timestamp
                        if message_age > 172800:  # 48 часов для обычных пользователей
                            delete_for_all = False
                            self.log(f"Message too old ({message_age}s), setting delete_for_all=False for private chat")

                    get_messages_controller().deleteMessages(
                        msgs_list, None, None,
                        pending_msg.chat_id, pending_msg.topic_id,
                        delete_for_all, 0
                    )

                    if self.get_setting("show_notifications", True):
                        BulletinHelper.show_success("Сообщение удалено")

                    self.log(f"Successfully deleted message {pending_msg.message_id} in chat {pending_msg.chat_id}")

                except Exception as e:
                    self.log(f"Error deleting message in main thread: {e}")
                    self.log(f"Traceback: {traceback.format_exc()}")
                    if self.get_setting("show_notifications", True):
                        BulletinHelper.show_error("Ошибка удаления сообщения")

            # Запускаем удаление в главном потоке
            run_on_ui_thread(delete_in_main_thread)

        except Exception as e:
            self.log(f"Error in _delete_message: {e}")
            self.log(f"Traceback: {traceback.format_exc()}")
                
    def _get_chat_id_from_peer(self, peer: Any) -> int:
        """Получение ID чата из peer объекта"""
        try:
            if hasattr(peer, 'channel_id') and peer.channel_id != 0:
                return -peer.channel_id
            elif hasattr(peer, 'chat_id') and peer.chat_id != 0:
                return -peer.chat_id
            elif hasattr(peer, 'user_id') and peer.user_id != 0:
                return peer.user_id
        except Exception as e:
            self.log(f"Error getting chat ID from peer: {e}")
        return 0
        
    def _get_topic_id(self, params: Any) -> int:
        """Получение ID топика из параметров сообщения"""
        try:
            if hasattr(params, 'replyToTopMsg') and params.replyToTopMsg:
                if hasattr(params.replyToTopMsg, 'messageOwner'):
                    return getattr(params.replyToTopMsg.messageOwner, 'id', 0)
                return getattr(params.replyToTopMsg, 'id', 0)
        except Exception as e:
            self.log(f"Error getting topic ID: {e}")
        return 0

    def _save_pending_tasks(self):
        """Сохранение задач в постоянное хранилище"""
        try:
            tasks_data = []
            with self.lock:
                for pending_msg in self.pending_messages.values():
                    tasks_data.append(pending_msg.to_dict())

            # Сохраняем в настройки плагина
            tasks_json = json.dumps(tasks_data)
            self.set_setting(self.STORAGE_KEY, tasks_json)
            self.log(f"Saved {len(tasks_data)} pending tasks to storage")

        except Exception as e:
            self.log(f"Error saving pending tasks: {e}")
            self.log(f"Traceback: {traceback.format_exc()}")

    def _load_pending_tasks(self):
        """Загрузка задач из постоянного хранилища"""
        try:
            tasks_json = self.get_setting(self.STORAGE_KEY, "[]")
            tasks_data = json.loads(tasks_json)

            current_time = time.time()
            loaded_count = 0
            expired_count = 0

            with self.lock:
                for task_data in tasks_data:
                    try:
                        pending_msg = PendingMessage.from_dict(task_data)

                        # Проверяем, не истекло ли время удаления
                        if pending_msg.delete_time <= current_time:
                            # Задача просрочена, выполняем удаление немедленно
                            self.log(f"Found expired task for message {pending_msg.message_id}, deleting immediately")
                            run_on_queue(lambda pm=pending_msg: self._delete_message(pm), PLUGINS_QUEUE)
                            expired_count += 1
                        else:
                            # Задача еще актуальна, добавляем в список
                            key = f"{pending_msg.chat_id}_{pending_msg.message_id}"
                            self.pending_messages[key] = pending_msg
                            loaded_count += 1

                    except Exception as e:
                        self.log(f"Error loading task data: {e}")

            self.log(f"Loaded {loaded_count} pending tasks, {expired_count} expired tasks processed")

            # Если были изменения, сохраняем обновленный список
            if expired_count > 0:
                self._save_pending_tasks()

        except Exception as e:
            self.log(f"Error loading pending tasks: {e}")
            self.log(f"Traceback: {traceback.format_exc()}")

    def _start_background_processor(self):
        """Запуск фонового потока для обработки задач"""
        try:
            self.stop_background_thread = False
            self.background_thread = threading.Thread(target=self._background_processor, daemon=True)
            self.background_thread.start()
            self.log("Background processor thread started")
        except Exception as e:
            self.log(f"Error starting background processor: {e}")

    def _background_processor(self):
        """Фоновый поток для периодической проверки и выполнения задач"""
        self.log("Background processor started")

        while not self.stop_background_thread:
            try:
                current_time = time.time()
                tasks_to_delete = []

                # Проверяем задачи на выполнение
                with self.lock:
                    for key, pending_msg in list(self.pending_messages.items()):
                        if pending_msg.delete_time <= current_time:
                            tasks_to_delete.append(pending_msg)
                            del self.pending_messages[key]

                # Выполняем удаление задач
                for pending_msg in tasks_to_delete:
                    self.log(f"Background processor: deleting message {pending_msg.message_id}")
                    try:
                        self._delete_message(pending_msg)
                    except Exception as e:
                        self.log(f"Error in background deletion: {e}")

                # Если были изменения, сохраняем
                if tasks_to_delete:
                    self._save_pending_tasks()

                # Очищаем старые pending_sends (старше 30 секунд)
                self._cleanup_old_pending_sends()

                # Спим 5 секунд перед следующей проверкой
                for _ in range(50):  # 5 секунд = 50 * 0.1 секунды
                    if self.stop_background_thread:
                        break
                    time.sleep(0.1)

            except Exception as e:
                self.log(f"Error in background processor: {e}")
                self.log(f"Traceback: {traceback.format_exc()}")
                time.sleep(1)  # Пауза при ошибке

        self.log("Background processor stopped")

    def _cleanup_old_pending_sends(self):
        """Очистка старых pending_sends и bulletin_shown_messages для предотвращения утечек памяти"""
        try:
            current_time = time.time()
            with self.lock:
                # Очищаем старые pending_sends
                keys_to_remove = []
                for key, send_data in self.pending_sends.items():
                    if current_time - send_data['timestamp'] > 30:  # Старше 30 секунд
                        keys_to_remove.append(key)

                for key in keys_to_remove:
                    del self.pending_sends[key]

                # Очищаем старые записи о показанных уведомлениях
                # Оставляем только те, которые соответствуют активным pending_messages
                active_keys = set(self.pending_messages.keys())
                self.bulletin_shown_messages &= active_keys

                if keys_to_remove:
                    self.log(f"Cleaned up {len(keys_to_remove)} old pending sends")

        except Exception as e:
            self.log(f"Error cleaning up pending sends: {e}")
